#!/bin/python3

import os
import sys
import inspect
import threading
import src.log as log
from src.service import service
from src.utils import utils

if __name__ == "__main__":

    # initial working directory
    workdir = utils.get_workdir()
    if not os.path.exists(workdir):
        os.mkdir(workdir)

    # add the src directory to the path
    sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/..")

    import src.applet as container
    context = container.applet("pandapy", "0.1")
    context.init()

    # initialize system config
    import src.config as config
    conf_default_path = config.config.CONFIG_DIR
    conf_sysconf_path = os.path.join(workdir, "config")
    context.set_config(config.default())
    if not os.path.exists(conf_sysconf_path):
        os.mkdir(conf_sysconf_path)
        context.get_config().dump(conf_sysconf_path, item={"machine", "joystick", "server", "system"})
    # apply the override config if exists
    context.get_config().load_overlay(conf_sysconf_path, item={"machine", "joystick", "server", "system"})

    # scan services in directory
    services = [
        "http.nginx",
        "http.backend",
        "controller.service",
        "job.manager",
        "hid.watcher",
    ]

    for file in services:
            
        try:
            module = __import__(f"src.{file}", fromlist=["service"])

            # enumerate all classes in the module
            for _name, _obj in inspect.getmembers(module, inspect.isclass):

                # check if the class is a subclass of service
                if issubclass(_obj, service) and _obj is not service:
                    context.log_info(f"found service class `{_name}` in `{file}`")
                    
                    # create an instance and mount it
                    context.mount(_obj(context))

        except ImportError as e:
            context.log_info(f"failed to import service `{file}`: {e}")
            continue
    
    # start the main thread
    _main_thread = threading.Thread(target=context.start)
    _main_thread.start()
    _main_thread.join()
